"""
AI Contact Research Agent - FastAPI Backend
Main application entry point with API routes and WebSocket support.
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse, FileResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import pandas as pd
import json
import os
import uuid
from datetime import datetime
import asyncio
import logging

# Import our custom modules
# from agent import ContactResearchAgent  # Temporarily disabled for testing
from models import (
    ColumnMapping,
    ResearchCommand,
    ContactRecord,
    APIConfig,
    ResearchProgress
)
# from config import Settings  # Temporarily disabled for testing

# Initialize FastAPI app
app = FastAPI(
    title="AI Contact Research Agent",
    description="Automated contact research and data enrichment system",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Initialize settings and global variables
# settings = Settings()  # Temporarily disabled for testing
research_agent = None
active_connections: List[WebSocket] = []
current_research_session: Optional[str] = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# In-memory storage (replace with database in production)
uploaded_files: Dict[str, Dict] = {}
column_mappings: Dict[str, ColumnMapping] = {}
contact_data: Dict[str, List[ContactRecord]] = {}
api_configurations: Dict[str, APIConfig] = {}

class ConnectionManager:
    """Manages WebSocket connections for real-time updates"""

    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                # Remove broken connections
                self.active_connections.remove(connection)

manager = ConnectionManager()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "AI Contact Research Agent API",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/upload-csv")
async def upload_csv(file: UploadFile = File(...)):
    """Handle CSV file upload and return preview data"""
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="Only CSV files are allowed")

        # Read CSV content
        content = await file.read()

        # Create unique file ID
        file_id = str(uuid.uuid4())

        # Parse CSV with pandas
        try:
            df = pd.read_csv(pd.io.common.StringIO(content.decode('utf-8')))
            # Replace NaN values with None for JSON serialization
            df = df.where(pd.notnull(df), None)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Error parsing CSV: {str(e)}")

        # Store file info
        file_info = {
            "id": file_id,
            "filename": file.filename,
            "upload_time": datetime.now().isoformat(),
            "row_count": len(df),
            "column_count": len(df.columns),
            "columns": df.columns.tolist(),
            "preview": df.head(5).to_dict('records'),
            "data": df.to_dict('records')  # Store full data
        }

        uploaded_files[file_id] = file_info

        # Broadcast upload success
        await manager.broadcast(json.dumps({
            "type": "file_uploaded",
            "file_id": file_id,
            "filename": file.filename,
            "row_count": len(df)
        }))

        return {
            "file_id": file_id,
            "filename": file.filename,
            "row_count": len(df),
            "column_count": len(df.columns),
            "columns": df.columns.tolist(),
            "preview": df.head(5).to_dict('records')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in upload_csv: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/map-columns")
async def map_columns(mapping: ColumnMapping):
    """Save column mapping configuration"""
    try:
        # Validate that file exists
        if mapping.file_id not in uploaded_files:
            raise HTTPException(status_code=404, detail="File not found")

        # Store mapping
        column_mappings[mapping.file_id] = mapping

        # Broadcast mapping saved
        await manager.broadcast(json.dumps({
            "type": "columns_mapped",
            "file_id": mapping.file_id,
            "mapping": mapping.dict()
        }))

        return {"message": "Column mapping saved successfully", "file_id": mapping.file_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in map_columns: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/start-research")
async def start_research(command: ResearchCommand):
    """Begin AI research process"""
    global current_research_session, research_agent

    try:
        # Validate prerequisites
        if command.file_id not in uploaded_files:
            raise HTTPException(status_code=404, detail="File not found")

        if command.file_id not in column_mappings:
            raise HTTPException(status_code=400, detail="Column mapping not configured")

        if not research_agent:
            raise HTTPException(status_code=400, detail="Research agent not configured. Please set up API keys first.")

        # Create new research session
        session_id = str(uuid.uuid4())
        current_research_session = session_id

        # Start research in background
        asyncio.create_task(run_research_session(session_id, command))

        return {
            "message": "Research started successfully",
            "session_id": session_id,
            "file_id": command.file_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting research: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

async def run_research_session(session_id: str, command: ResearchCommand):
    """Run the research session in background"""
    try:
        file_data = uploaded_files[command.file_id]
        mapping = column_mappings[command.file_id]

        # Get the data to research
        df = pd.DataFrame(file_data["data"])

        # Process each company
        total_companies = len(df)
        processed = 0

        for index, row in df.iterrows():
            if current_research_session != session_id:
                # Session was cancelled
                break

            company_name = row.get(mapping.company_name_column)
            if not company_name:
                continue

            # Send progress update
            await manager.broadcast(json.dumps({
                "type": "research_progress",
                "session_id": session_id,
                "current_company": company_name,
                "processed": processed,
                "total": total_companies,
                "percentage": round((processed / total_companies) * 100, 1)
            }))

            # Research this company (placeholder - will implement with actual agent)
            await asyncio.sleep(2)  # Simulate research time

            processed += 1

        # Research completed
        await manager.broadcast(json.dumps({
            "type": "research_completed",
            "session_id": session_id,
            "processed": processed,
            "total": total_companies
        }))

    except Exception as e:
        logger.error(f"Error in research session: {e}")
        await manager.broadcast(json.dumps({
            "type": "research_error",
            "session_id": session_id,
            "error": str(e)
        }))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Handle incoming WebSocket messages if needed
            logger.info(f"Received WebSocket message: {data}")
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.post("/config")
async def save_config(config: APIConfig):
    """Save API configuration"""
    global research_agent

    try:
        # Store configuration
        api_configurations["current"] = config

        # Initialize research agent with new config (temporarily disabled for testing)
        # research_agent = ContactResearchAgent(
        #     openrouter_key=config.openrouter_api_key,
        #     gemini_key=config.gemini_api_key,
        #     rate_limit=config.rate_limit_per_minute,
        #     openrouter_model=config.openrouter_model,
        #     gemini_model=config.gemini_model
        # )

        return {"message": "Configuration saved successfully"}

    except Exception as e:
        logger.error(f"Error saving config: {e}")
        raise HTTPException(status_code=500, detail="Failed to save configuration")

@app.post("/chat-command")
async def chat_command(command: dict):
    """Handle chat commands from the frontend"""
    try:
        message = command.get("message", "")
        file_id = command.get("file_id")

        # For now, return a simple response
        # In a full implementation, this would process the command with the AI agent
        return {
            "response": f"Received command: {message}",
            "status": "success"
        }
    except Exception as e:
        logger.error(f"Error in chat command: {e}")
        raise HTTPException(status_code=500, detail="Failed to process command")

@app.get("/files")
async def list_files():
    """Get list of uploaded files"""
    return {
        "files": [
            {
                "id": file_id,
                "filename": info["filename"],
                "upload_time": info["upload_time"],
                "row_count": info["row_count"],
                "column_count": info["column_count"]
            }
            for file_id, info in uploaded_files.items()
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
